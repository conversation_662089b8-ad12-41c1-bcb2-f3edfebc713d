import { h, reactive, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { baseColumns, createSorter, createDateSorter, createNumberSorter } from "@/view/common/baseColumns";
import { productClassify } from '@/view/common/constant'
import { useColumnsRender } from "../../../common/useColumnsRender";
import { useMerchant } from "@/view/common/useMerchant";
// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  // 检查是否有小数部分
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }
  const hasDecimal = numValue % 1 !== 0;
  if (hasDecimal) {
    // 如果有小数，保留原有小数位数
    return new Intl.NumberFormat('zh-CN').format(numValue);
  } else {
    // 如果没有小数，补充.00
    return new Intl.NumberFormat('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(numValue);
  }
};
const { baseColumnsExport, baseColumnsShow } = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions, getMerchantOptions } = useMerchant()
// 初始化时获取数据
await getMerchantOptions()
function getColumns() {
  const commColumns = reactive([
    'businessType'
    , 'id'
    , 'planNo'
    , 'businessType'
    , 'contractNo'
    , 'businessLocation'
    , 'buyer'
    , 'seller'
    , 'manufacturer'
    , 'domesticClient'
    , 'estReceiveDate'
    , 'receiveStatus'
    , 'estPaymentDate'
    , 'paymentStatus'
    , 'estArbitrationDate'
    , 'arbitrationStatus'
    , 'estLicenseDate'
    , 'licenseStatus'
    , 'estTransportCertDate'
    , 'transportCertStatus'
    , 'estInsuranceDate'
    , 'insuranceStatus'
    , 'entryStatus'
    , 'estPackingInfo'
    , 'licenseNo'
    , 'licenseApplyDate'
    , 'licenseValidityDate'
    , 'licenseRemark'
    , 'remark'
    , 'status'
    , 'apprStatus'
    , 'confirmTime'
    , 'versionNo'
    , 'tradeCode'
    , 'sysOrgCode'
    , 'parentId'
    , 'createBy'
    , 'createTime'
    , 'updateBy'
    , 'updateTime'
    , 'createUserName'
    , 'updateUserName'
    , 'extend1'
    , 'extend2'
    , 'extend3'
    , 'extend4'
    , 'extend5'
    , 'extend6'
    , 'extend7'
    , 'extend8'
    , 'extend9'
    , 'extend10'
  ])
  // 导出字段设置
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])
  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])
  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth: 80,
      width: 80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '计划书单号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'planNo',
      resizable: true,
      key: 'planNo',
    },
    {
      title: '合同号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'contractNo',
      resizable: true,
      key: 'contractNo',
    },
    {
      title: '买家',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'buyer',
      resizable: true,
      key: 'buyer',
      customRender: ({ text }) => {
        // 对于客商字段，由于数据是异步获取的，我们需要在merchantOptions中查找对应的label
        const merchant = merchantOptions.value.find(item => item.value === text);
        return merchant ? `${merchant.value} ${merchant.label}` : text;
      }
    },
    {
      title: '卖家',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'seller',
      resizable: true,
      key: 'seller',
      customRender: ({ text }) => {
        // 对于客商字段，由于数据是异步获取的，我们需要在merchantOptions中查找对应的label
        const merchant = merchantOptions.value.find(item => item.value === text);
        return merchant ? `${merchant.value} ${merchant.label}` : text;
      }
    },
    {
      title: '收款状态',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'receiveStatus',
      resizable: true,
      key: 'receiveStatus',
      customRender: ({ text }) => {
        return cmbShowRender(text, productClassify.receiveStatus);
      }
    },
    {
      title: '付款状态',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'paymentStatus',
      resizable: true,
      key: 'paymentStatus',
      customRender: ({ text }) => {
        return cmbShowRender(text, productClassify.paymentStatus);
      }
    },
    {
      title: '预裁定状态',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'arbitrationStatus',
      resizable: true,
      key: 'arbitrationStatus',
      customRender: ({ text }) => {
        return cmbShowRender(text, productClassify.arbitrationStatus);
      }
    },
    {
      title: '许可证状态',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'licenseStatus',
      resizable: true,
      key: 'licenseStatus',
      customRender: ({ text }) => {
        return cmbShowRender(text, productClassify.licenseStatus);
      }
    },
    {
      title: '准运证状态',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'transportCertStatus',
      resizable: true,
      key: 'transportCertStatus',
      customRender: ({ text }) => {
        return cmbShowRender(text, productClassify.transportCertStatus);
      }
    },
    {
      title: '报关状态',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'entryStatus',
      resizable: true,
      key: 'entryStatus',
      customRender: ({ text }) => {
        return cmbShowRender(text, productClassify.entryStatus);
      }
    },
    {
      title: '保险状态',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'insuranceStatus',
      resizable: true,
      key: 'insuranceStatus',
      customRender: ({ text }) => {
        return cmbShowRender(text, productClassify.insuranceStatus);
      }
    },
    {
      title: '制单人',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'createUserName',
      resizable: true,
      key: 'createUserName',
    },
    {
      title: '制单时间',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'insertTime',
      resizable: true,
      key: 'insertTime',
    },
    {
      title: '单据状态',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'status',
      resizable: true,
      key: 'status',
      customRender: ({ text }) => {
        const tagColor = text === '2' ? 'error' : 'success';
        return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.data_status))
      }
    },
    {
      title: '审核状态',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'apprStatus',
      resizable: true,
      key: 'apprStatus',
      customRender: ({ text }) => {
        return cmbShowRender(text, productClassify.orderApprStatus);
      }
    },
    {
      title: '确认时间',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'confirmTime',
      resizable: true,
      key: 'confirmTime',
    },

  ])
  return {
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
export { getColumns }
