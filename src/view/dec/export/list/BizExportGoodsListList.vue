 <template>
  <!-- 第9条线-非国营贸易出口辅料-出货信息表体（商品信息） -->
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
          <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:add']">
            <a-button size="small" @click="handlerAdd" :disabled="!props.isEdit">
              <template #icon>
                <GlobalIcon type="plus" style="color:green"/>
              </template>
              {{localeContent('m.common.button.add')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:delete']">
            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete" :disabled="!props.isEdit">
              <template #icon>
                <GlobalIcon type="delete" style="color:red"/>
              </template>
              {{localeContent('m.common.button.delete')}}
            </a-button>
          </div>
      </div>

      <!-- 表格区域 -->
      <div>
        <s-table
          ref="tableRef"
          class="cs-action-item-modal-table remove-table-border-add-bg"
          size="small"
          :height="500"
          :scroll="{ y: '100%',x:400 }"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
          :animate-rows="false"
        >
          <!-- 空数据                  :precision="6"-->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>
          <template #bodyCell="{text, record, index, column, key }">
            <!-- 数量 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'qty'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].qty"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"

              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>

            <!-- 单价 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'price'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].price"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>


          <!-- 起始箱号 -->
          <template v-if="(!props.showDisable) && column.dataIndex === 'boxStartNo'">
            <a-input-number
              v-if="props.isEdit === true"
              size="small"
              v-model:value="dataSourceList[index].boxStartNo"
              style="width: 100%;height: 24px"
              @blur="() => {
                handlerTableInnerChange(record,column);
              }"
              @keydown.enter="() => {
                handlerTableInnerChange(record,column);
              }"
              :precision="0"
            />
            <span v-else>{{text}}</span>
          </template>

          <!-- 终止箱号 -->
          <template v-if="(!props.showDisable) && column.dataIndex === 'endStartNo'">
            <a-input-number
              v-if="props.isEdit === true"
              size="small"
              v-model:value="dataSourceList[index].endStartNo"
              style="width: 100%;height: 24px"
              @blur="() => {
                handlerTableInnerChange(record,column);
              }"
              @keydown.enter="() => {
                handlerTableInnerChange(record,column);
              }"
              :precision="0"
            />
            <span v-else>{{text}}</span>
          </template>



           <!-- 净重 -->
           <template v-if="(!props.showDisable) && column.dataIndex === 'netWeight'">
            <a-input-number
              v-if="props.isEdit === true"
              size="small"
              v-model:value="dataSourceList[index].netWeight"
              :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
              :parser="value => inputParser(value)"
              style="width: 100%;height: 24px"
              @blur="() => {
                handlerTableInnerChange(record,column);
              }"
              @keydown.enter="() => {
                handlerTableInnerChange(record,column);
              }"
            />
            <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
          </template>



            <!-- 毛重 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'grossWeight'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].grossWeight"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>


            <!-- 修改长 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'eLength'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].eLength"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"

              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>


            <!-- 修改宽 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'eWidth'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].eWidth"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>

            <!-- 修改高 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'eHeight'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].eHeight"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"

              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>


            <!-- 修改数量（卷）-->
            <template v-if="(!props.showDisable) && column.dataIndex === 'qtyJ'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].qtyJ"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>


          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <!-- 汇总信息 -->
        <div class="cs-margin-right cs-list-total-data ">
          数量：{{formatSpecifiedNumber(totalData.qtyTotal,true,2)}} ，金额：{{formatSpecifiedNumber(totalData.totalAmount,true,2)}}，毛重(KG)：{{formatSpecifiedNumber(totalData.grossTotal,true,2)}}，净重(KG)：{{formatSpecifiedNumber(totalData.netTotal,true,2)}}，皮重(KG)：{{formatSpecifiedNumber(totalData.traeTotal,true,2)}}
        </div>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>



  </section>


</template>

<script setup>
  /* 使用自定义 Hook 函数 */
  import {useCommon} from '@/view/common/useCommon'
  import {createVNode, onMounted, provide, reactive, ref} from "vue";
  import {message, Modal} from "ant-design-vue";
  import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
  import {localeContent} from "@/view/utils/commonUtil";
  import ycCsApi from "@/api/ycCsApi";
  import {isNullOrEmpty} from "@/view/utils/common";
  import { useColumnsRender } from "@/view/common/useColumnsRender";
  const  { inputFormatter, inputParser,formatSpecifiedNumber,cmbShowRender } = useColumnsRender()

  /* 引入通用方法 */
  const {
    show,
    dataSourceList,
    tableLoading,
    getTableScroll
  } = useCommon()



  defineOptions({
    name: 'BizExportGoodsListList',
  });

  const props = defineProps({
    parentId:{
      type: String,
      required: true,
      default: ''
    },
    isAllConfirmed:{
      type:Boolean,
      default:()=>false
    },
    isEdit:{
      type:Boolean,
      default:()=>false
    },
    /* 是否查看模式 */
    showDisable:{
      type: Boolean,
      default: () => false
    }

  })

  const page = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      title: '商品名称',
      width: 200,
      align: 'center',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '商品描述',
      width: 200,
      align: 'center',
      dataIndex: 'productDesc',
      key: 'productDesc',
    },
    {
      title:'规格',
      width: 200,
      align: 'center',
      dataIndex: 'specification',
      key: 'specification',
      autoHeight: true,
      resizable: true,
    },
    {
      title:'数量（卷）',
      width: 200,
      align: 'center',
      dataIndex: 'qtyJ',
      key: 'qtyJ',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
    },
    {
      title: '数量（吨）',
      width: 200,
      align: 'center',
      dataIndex: 'qty',
      key: 'qty',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '单位',
      width: 200,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      customRender: ({ text }) => {
        return cmbShowRender(text,unitList.value)
      }
    },
    {
      title: '单价',
      width: 200,
      align: 'center',
      dataIndex: 'price',
      key: 'price',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '金额',
      width: 200,
      align: 'center',
      dataIndex: 'amount',
      key: 'amount',
    },
    {
      title: '起始箱号',
      width: 200,
      align: 'center',
      dataIndex: 'boxStartNo',
      key: 'boxStartNo',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
    },
    {
      title: '结束箱号',
      width: 200,
      align: 'center',
      dataIndex: 'endStartNo',
      key: 'endStartNo',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
    },
    {
      title: '包装样式',
      width: 200,
      align: 'center',
      dataIndex: 'packageStyle',
      key: 'packageStyle',
      customRender: ({ text }) => {
        return cmbShowRender(text,packageList.value)
      }
    },
    {
      title: '毛重(KG)',
      width: 200,
      align: 'center',
      dataIndex: 'grossWeight',
      key: 'grossWeight',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '净重(KG)',
      width: 200,
      align: 'center',
      dataIndex: 'netWeight',
      key: 'netWeight',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '皮重(KG)',
      width: 200,
      align: 'center',
      dataIndex: 'tareWeight',
      key: 'tareWeight',
    },
    {
      title: '长(M)',
      width: 200,
      align: 'center',
      dataIndex: 'eLength',
      key: 'eLength',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '宽(M)',
      width: 200,
      align: 'center',
      dataIndex: 'eWidth',
      key: 'eWidth',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '高(M)',
      width: 200,
      align: 'center',
      dataIndex: 'eHeight',
      key: 'eHeight',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    }

  ])




  const tableHeight = ref('')

  /* 引入表单数据 */
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData:[],
    loading: false,
  });



  /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
  const onSelectChange = (selectedRowKeys, rowSelectData) => {
    gridData.selectedData = rowSelectData;
    gridData.selectedRowKeys = selectedRowKeys;
  };







  /* 按钮loading */
  const deleteLoading = ref(false)


  /* 删除数据 */
  const handlerDelete = () => {
    if (gridData.selectedRowKeys.length <= 0){
      message.warning('请选择一条数据')
      return
    }
    // 弹出确认框
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '删除',
      cancelText: '取消',
      content: '确认删除所选项吗？',
      onOk() {
        // deleteLoading.value = true
        // deleteClient(gridData.selectedRowKeys).then(res => {
        //   if (res.code === 200) {
        //     message.success("删除成功！")
        //     getList()
        //   }
        // }).finally(() => {
        //   deleteLoading.value = false
        // })
        window.majesty.httpUtil.deleteAction(`${ycCsApi.bizExportGoodsList.delete}/${gridData.selectedRowKeys}`).then(res => {
          if (res.code === 200) {
            message.success("删除成功！")
            getList()
            getListSumTotal()
          }else {
            message.error(res.message)
          }
        })
      },
      onCancel() {

      },
    });

  }

  const getList = async () => {
    try {
      tableLoading.value = true
      const res = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.list}?page=${page.current}&limit=${page.pageSize}`,{parentId:props.parentId})
      if (res.code === 200) {
        dataSourceList.value = res.data
        page.total = res.total
      }else {
        message.error(res.message)
      }
    }catch(err) {
      console.log(err)
    }finally {
      tableLoading.value = false
    }

  }

  // 获取表体汇总数据
  const totalData = reactive({
    qtyTotal: 0,
    totalAmount: 0,
    grossTotal: 0,
    netTotal: 0,
    traeTotal: 0
  })

  const getListSumTotal = () => {

    let parentId =  props.parentId
    let params = {
      parentId: parentId
    }
    if (isNullOrEmpty(parentId)) {
      return
    }

    window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.getSummary}/${parentId}`, params).then(res => {
      if (res.code === 200) {
        Object.assign(totalData, res.data);
      } else {
        message.error(res.message)
      }
    }).catch(err => {
      message.error(err.message)
    })
  }



  const onPageChange = (pageNumber, pageSize) =>{
    // console.log('PageNumber:', pageNumber)
    // console.log('PageNumber:', pageSize)
    // console.log('页码或者PageSize发生变化时触发')
    page.current = pageNumber
    page.pageSize = pageSize
    // 在这里添加处理页码变化的逻辑
    getList()
  }





  /* 行内编辑通用方法 */
  const isEditLoading = ref(false)

  /* 处理行内编辑 */
  const handlerTableInnerChange = async (record, column) => {
    if (isEditLoading.value === true) {
      console.log('回车，失焦同时触发！');
      return;
    }

    isEditLoading.value = true;

    if (!record || !record.id) {
      isEditLoading.value = false;
      return;
    }
    const res = await  window.majesty.httpUtil.getAction(`${ycCsApi.bizExportGoodsList.getListById}/${record.id}`,record);
    const dataTemp = res.data;
    try {

      if (res.code !== 200) {
        isEditLoading.value = false;
        return;
      }

      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改数量 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      // 如果必要情况下 可以取消 tableLoading
      if (column.dataIndex === 'qty') {
        if (isNullOrEmpty(dataTemp) || dataTemp.qty === record.qty) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateNumOrPrice}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.quantity',dataTemp.qty);
          record.qty = dataTemp.qty; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }

      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改单价 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'price') {
        if (isNullOrEmpty(dataTemp) || dataTemp.price === record.price) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateNumOrPrice}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.price',dataTemp.price);
          record.price = dataTemp.price; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改起始箱号 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'boxStartNo') {
        if (isNullOrEmpty(dataTemp) || dataTemp.boxStartNo === record.boxStartNo) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateStartBoxNo}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.boxStartNo',dataTemp.boxStartNo);
          record.boxStartNo = dataTemp.boxStartNo; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改结束箱号 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'endStartNo') {
        if (isNullOrEmpty(dataTemp) || dataTemp.endStartNo === record.endStartNo) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateEndBoxNo}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.endStartNo',dataTemp.endStartNo);
          record.endStartNo = dataTemp.endStartNo; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }

      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改净重 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'netWeight') {
        if (isNullOrEmpty(dataTemp) || dataTemp.netWeight === record.netWeight) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateNetWeight}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.netWeight',dataTemp.netWeight);
          record.netWeight = dataTemp.netWeight; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改毛重 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'grossWeight') {
        if (isNullOrEmpty(dataTemp) || dataTemp.grossWeight === record.grossWeight) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateGrossWeight}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.grossWeight',dataTemp.grossWeight);
          record.grossWeight = dataTemp.grossWeight; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改长度 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'eLength') {
        if (isNullOrEmpty(dataTemp) || dataTemp.eLength === record.eLength) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateGoodsLengthHeightWidth}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.length',dataTemp.eLength);
          record.eLength = dataTemp.eLength; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }

      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改宽度 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'eWidth') {
        if (isNullOrEmpty(dataTemp) || dataTemp.eWidth === record.eWidth) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateGoodsLengthHeightWidth}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.width',dataTemp.eWidth);
          record.eWidth = dataTemp.eWidth; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }

      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改高度 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'eHeight') {
        if (isNullOrEmpty(dataTemp) || dataTemp.eHeight === record.eHeight) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateGoodsLengthHeightWidth}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.height',dataTemp.eHeight);
          record.eHeight = dataTemp.eHeight; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改数量（卷） <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'qtyJ') {
        if (isNullOrEmpty(dataTemp) || dataTemp.qtyJ === record.qtyJ) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateQtyJ}`, record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.qtyJ', dataTemp.qtyJ);
          record.qtyJ = dataTemp.qtyJ; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }



    } catch (error) {
      // Object.assign(record, dataTemp)
      message.error(error.message);
    } finally {
      setTimeout(() => {
        isEditLoading.value = false;
      }, 100);
    }
  };



  const unitList = ref([])
  const packageList = ref([])
  const getCommonKeyValueList = async () => {
    try {
      const res = await window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsHead.getCommonKeyValueList,{});
      if (res.code === 200) {
        unitList.value = res.data.unitList;
        packageList.value = res.data.packageList;
      }else {
        message.error(res.message);
      }
    }catch(err) {
      console.log(err);
    }finally {
    }

  }



  onMounted(() => {


    // ajaxUrl.selectAllPage = ycCsApi.smoke_machine.list
    // ajaxUrl.exportUrl = ycCsApi.smoke_machine.export

    tableHeight.value = getTableScroll(100,'');

    getList()
    getListSumTotal()
    getCommonKeyValueList()


  })





</script>

<style lang="less" scoped>


</style>
