<template>
  <section>
    <!-- 第9条线-非国营贸易出口辅料-外销发票表头 -->
    <a-card size="small" title="外销发票表头" class="cs-card-form">
      <div class="cs-form">
        <a-spin   :spinning="saveLoading">
          <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class=" grid-container">

          <!-- 发票号，字符型(60)，文本控件，必填，同出货单号，允许修改 -->
          <a-form-item name="invoiceNo" :label="'发票号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.invoiceNo" allow-clear/>
          </a-form-item>

          <!-- 出货单号，字符型(60)，文本控件，必填，出货信息操作<确认>时带出，不允许修改 -->
          <a-form-item name="exportNo" :label="'出货单号'" class="grid-item" :colon="false">
            <a-input :disabled="true" allow-clear size="small" v-model:value="formData.exportNo"/>
          </a-form-item>

          <!-- 合同号，字符型(60)，文本控件，必填，出货信息操作<确认>时带出，不允许修改 -->
          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.contractNo" allow-clear/>
          </a-form-item>

          <!-- 发票客户，字符型(200)，文本控件，必填，出货信息操作<确认>时带出-客户，不允许修改 -->
          <a-form-item name="invoiceCustomer" :label="'发票客户'" class="grid-item" :colon="false">
            <cs-select
              :disabled="true"
              :options="customerList"
              :combine-display="true"
              option-filter-prop="label"
              option-label-prop="key"
              allow-clear
              show-search
              v-model:value="formData.invoiceCustomer"
              id="invoiceCustomer"
            />
          </a-form-item>

          <!-- 销售客户，字符型(200)，文本控件，必填，出货信息操作<确认>时带出-供应商，不允许修改 -->
          <a-form-item name="salesCustomer" :label="'销售客户'" class="grid-item" :colon="false">
            <cs-select
              :disabled="true"
              :options="customerList"
              :combine-display="true"
              option-filter-prop="label"
              option-label-prop="key"
              allow-clear
              show-search
              v-model:value="formData.salesCustomer"
              id="salesCustomer"
            />
          </a-form-item>

          <!-- 信用证号，字符型(60)，文本控件，非必填，用户录入 -->
          <a-form-item name="lcNo" :label="'信用证号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.lcNo" allow-clear/>
          </a-form-item>

          <!-- 币种，字符型(10)，下拉框控件，必填，出货信息操作<确认>时带出，不允许修改 -->
          <a-form-item name="currency" :label="'币种'" class="grid-item" :colon="false">
            <cs-select
              :options="currList"
              :combine-display="true"
              option-filter-prop="label"
              option-label-prop="key"
              allow-clear
              show-search
              :disabled="showDisable"
              v-model:value="formData.currency"
              id="currency"
            />
          </a-form-item>

          <!-- 合计金额，数值型(19,4)，文本控件，必填，操作<确认>时系统带出，不允许修改，汇总出货信息表体金额 -->
          <a-form-item name="totalAmount" :label="'合计金额'" class="grid-item" :colon="false">
            <a-input-number
                :disabled="true"
                size="small" v-model:value="formData.totalAmount"
                style="width: 100%"
                allow-clear
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
            />
          </a-form-item>

          <!-- 唛头，字符型(300)，文本控件，非必填，出货信息操作<确认>时带出，不允许修改 -->
          <a-form-item name="mark" :label="'唛头'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.mark" allow-clear/>
          </a-form-item>

          <!-- 代理费率%，数值型(19,6)，文本控件，必填，出货信息操作<确认>时带出，根据合同号关联【合同与协议】代理协议表头-代理费率，不允许修改 -->
          <a-form-item name="agentRate" :label="'代理费率%'" class="grid-item" :colon="false">
            <a-input-number
                  :disabled="true"
                  size="small"
                  v-model:value="formData.agentRate"
                  style="width: 100%"
                  addon-after="%"
                  allow-clear
                  :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                  :parser="value => inputParser(value)"
            />
          </a-form-item>

          <!-- 代理费（外币），数值型(19,6)，文本控件，必填，系统计算=合计金额*代理费率，不允许修改 -->
          <a-form-item name="agentFeeForeign" :label="'代理费（外币）'" class="grid-item" :colon="false">
            <a-input-number
                  :disabled="true"
                  size="small"
                  v-model:value="formData.agentFeeForeign"
                  style="width: 100%"
                  allow-clear
                  :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                  :parser="value => inputParser(value)"
            />
          </a-form-item>

          <!-- 预收款日期，日期型(10)，日期控件，非必填，用户录入 -->
          <a-form-item name="prepayDate" :label="'预收款日期'" class="grid-item" :colon="false">
            <a-date-picker
              v-model:value="formData.prepayDate"
              id="prepayDate"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              :disabled="showDisable"
              placeholder=""
            />
          </a-form-item>

          <!-- 作销日期，日期型(10)，日期控件，非必填，用户录入 -->
          <a-form-item name="cancelDate" :label="'作销日期'" class="grid-item" :colon="false">
            <a-date-picker
              v-model:value="formData.cancelDate"
              id="cancelDate"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              :disabled="showDisable"
              placeholder=""
            />
          </a-form-item>

          <!-- 发票日期，日期型(10)，日期控件，非必填，用户录入 -->
          <a-form-item name="invoiceDate" :label="'发票日期'" class="grid-item" :colon="false">
            <a-date-picker
              v-model:value="formData.invoiceDate"
              id="invoiceDate"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              :disabled="showDisable"
              placeholder=""
            />
          </a-form-item>

          <!-- 汇率，数值型(19,6)，文本控件，必填，根据当前月份+币种关联【企业自定义参数-企业汇率】取"汇率"栏位值，允许修改；<新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
          <a-form-item name="exchangeRate" :label="'汇率'" class="grid-item" :colon="false">
            <a-input-number
                  :disabled="showDisable"
                  size="small"
                  v-model:value="formData.exchangeRate"
                  style="width: 100%"
                  allow-clear
                  :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                  :parser="value => inputParser(value)"
            />
          </a-form-item>

          <!-- 代理费（不含税金额），数值型(19,2)，文本控件，必填，系统计算=代理费（价税合计）-代理费（不含税金额），当代理费（价税合计）变化时，要重新计算该栏位，不允许修改 -->
          <a-form-item name="agentFeeExTax" :label="'代理费（不含税金额）'" class="grid-item" :colon="false">
            <a-input-number
                  :disabled="true"
                  size="small"
                  v-model:value="formData.agentFeeExTax"
                  style="width: 100%"
                  allow-clear
                  :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                  :parser="value => inputParser(value)"
            />
          </a-form-item>

          <!-- 代理费税额，数值型(19,2)，文本控件，必填，系统计算=代理费（价税合计）*0.06，当代理费（价税合计）变化时，要重新计算该栏位，不允许修改 -->
          <a-form-item name="agentTax" :label="'代理费税额'" class="grid-item" :colon="false">
            <a-input-number
                  :disabled="true"
                  size="small"
                  v-model:value="formData.agentTax"
                  style="width: 100%"
                  allow-clear
                  :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                  :parser="value => inputParser(value)"
            />
          </a-form-item>

          <!-- 代理费（价税合计），数值型(19,6)，文本控件，必填，系统计算=代理费（外币）*汇率，不允许修改 -->
          <a-form-item name="agentFeeTotal" :label="'代理费（价税合计）'" class="grid-item" :colon="false">
            <a-input-number
                  :disabled="true"
                  size="small"
                  v-model:value="formData.agentFeeTotal"
                  style="width: 100%"
                  allow-clear
                  :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                  :parser="value => inputParser(value)"
            />
          </a-form-item>

          <!-- 备注，字符型(200)，文本控件，非必填，用户录入 -->
          <a-form-item name="remark" :label="'备注'" class="grid-item merge-3" :colon="false">
            <a-textarea
              :disabled="showDisable"
              size="small"
              v-model:value="formData.remark"
              :auto-size="{ minRows: 2, maxRows: 3 }"
              allow-clear
            />
          </a-form-item>

          <!-- 发送财务系统，字符型(10)，下拉框控件，必填，0是1否，默认为是，允许修改 -->
          <a-form-item name="sendFinance" :label="'发送财务系统'" class="grid-item" :colon="false">
            <cs-select
              :options="productClassify.yesOrNoStatus"
              :combine-display="true"
              option-filter-prop="label"
              option-label-prop="key"
              allow-clear
              :disabled="true"
              show-search
              v-model:value="formData.sendFinance"
              id="sendFinance"
            />
          </a-form-item>


          <!-- 是否红冲，字符型(10)，下拉框控件，必填，0是1否，新增数据默认为1否；操作<红冲>成功时，改为0是，不允许修改 -->
          <a-form-item name="isRedFlush" :label="'是否红冲'" class="grid-item" :colon="false">
            <cs-select
              :options="productClassify.yesOrNoStatus"
              :combine-display="true"
              option-filter-prop="label"
              option-label-prop="key"
              allow-clear
              :disabled="true"
              show-search
              v-model:value="formData.isRedFlush"
              id="isRedFlush"
            />
          </a-form-item>

          <!-- 单据状态，字符型(10)，下拉框控件，非必填，0编制：未点击外销发票列表的"确认"功能按钮；1确认：点击外销发票列表的"确认"功能按钮，且成功提交；2作废：点击外销发票列表的"作废"功能按钮，且成功提交；不允许修改，置灰 -->
          <a-form-item name="dataState" :label="'单据状态'" class="grid-item" :colon="false">
            <cs-select
              :options="productClassify.orderStatus"
              :combine-display="true"
              option-filter-prop="label"
              option-label-prop="key"
              allow-clear
              :disabled="true"
              show-search
              v-model:value="formData.dataState"
              id="dataState"
            />
          </a-form-item>


          <!-- 确认时间，日期型(18)，日期控件，非必填，点击外销发票列表的"确认"功能按钮，且成功提交的时间：yyyy-mm-dd hh:mm:ss，不允许修改，置灰 -->
          <a-form-item name="confirmTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker
              v-model:value="formData.confirmTime"
              id="confirmTime"
              size="small"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              style="width: 100%"
              placeholder=""
              :disabled="true"
            />
          </a-form-item>


          <!-- 制单人 -->
          <a-form-item name="createBy" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.createBy" allow-clear/>
          </a-form-item>

          <!-- 制单时间 -->
          <a-form-item name="createTime" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              v-model:value="formData.createTime"
              id="createTime"
              size="small"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              style="width: 100%"
              placeholder=""
              :disabled="true"
            />
          </a-form-item>


          <div class="cs-submit-btn merge-3">
            <!-- 保存 -->
            <a-button size="small"
                      type="primary"
                      @click="handlerSave"
                      class="cs-margin-right"
                      :disabled="showDisable"
                      :loading="saveLoading"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW ">保存
            </a-button>

            <!-- 返回 -->
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(false)">返回</a-button>

            <!-- 确认 -->
            <a-button size="small" type="ghost"
                      @click="handleConfirm"
                      class="cs-margin-right"
                      :loading="confirmOrderLoading"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW ">
              <template #icon>
                <GlobalIcon class="btn-icon" type="check" style="font-size: 12px;"/>
              </template>
              <template #default>
                确认
              </template>
            </a-button>

            <!-- 红冲 -->
            <a-button size="small" type="ghost"
                      @click="handleRedFlush"
                      class="cs-margin-right"
                      :loading="redFlushLoading"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW ">
              <template #icon>
                <GlobalIcon class="btn-icon" type="retweet" style="font-size: 12px;"/>
              </template>
              <template #default>
                红冲
              </template>
            </a-button>


            <!-- 退单 -->
            <a-button size="small" type="ghost"
                      @click="handleBackOrder"
                      class="cs-margin-right"
                      :loading="backOrderLoading"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW ">
              <template #icon>
                <GlobalIcon class="btn-icon" type="close" style="font-size: 12px;"/>
              </template>
              <template #default>
                退单
              </template>
            </a-button>
          </div>
        </a-form>
        </a-spin>
      </div>
    </a-card>


    <!-- 第9条线-非国营贸易出口辅料-外销发票表头 -->
    <a-card size="small" title="外销发票表体" class="cs-card-form">
      <div class="cs-form">
        <biz-export-goods-sell-list-list :parent-id="formData.id"  v-if="formData && formData.id" />
      </div>
    </a-card>


  </section>
</template>

<script setup>
  import {editStatus, productClassify} from '@/view/common/constant'
  import {message, Modal} from "ant-design-vue";
  import {createVNode, onMounted, reactive, ref, watch} from "vue";
  import CsSelect from "@/components/select/CsSelect.vue";
  import {usePCode} from "@/view/common/usePCode";
  import BizExportGoodsSellListList from "@/view/dec/export/invoice/BizExportGoodsSellListList.vue";
  import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
  import ycCsApi from "@/api/ycCsApi";
  import useEventBus from "@/view/common/eventBus";
  import {isNullOrEmpty} from "@/view/utils/common";
  const { emitEvent } = useEventBus();
  import { useColumnsRender } from "@/view/common/useColumnsRender";
  const  { inputFormatter, inputParser,formatSpecifiedNumber,cmbShowRender } = useColumnsRender()
  const {getPCode} = usePCode()


  const props = defineProps({
    editConfig: {
      type: Object,
      default: () => {
      }
    },
    operationStatus: {
      type: String,
      default: ''
    },
    headId: {
      type: String,
      default: ''
    },
    isAllConfirmed: {
      type: Boolean,
      default: false
    }
  });

  // 定义子组件 emit事件，用于子组件向父组件传递数据
  const emit = defineEmits(['onBack']);

  const onBack = (val) => {
    emit('onBack', val);
  };

  // 是否禁用
  const showDisable = ref(false)

  // 表单数据
  const formData = reactive({
    // 主键id
    id: '',
    // 业务类型
    businessType: '',
    // 数据状态
    dataState: '',
    // 版本号
    versionNo: '',
    // 企业10位编码
    tradeCode: '',
    // 组织机构代码
    sysOrgCode: '',
    // 父级id
    parentId: '',
    // 创建人
    createBy: '',
    // 创建时间
    createTime: '',
    createTimeTo: '',
    createTimeForm: '',
    // 更新人
    updateBy: '',
    // 更新时间
    updateTime: '',
    updateTimeTo: '',
    updateTimeForm: '',
    // 插入用户名
    insertUserName: '',
    // 更新用户名
    updateUserName: '',
    // 扩展字段1
    extend1: '',
    // 扩展字段2
    extend2: '',
    // 扩展字段3
    extend3: '',
    // 扩展字段4
    extend4: '',
    // 扩展字段5
    extend5: '',
    // 扩展字段6
    extend6: '',
    // 扩展字段7
    extend7: '',
    // 扩展字段8
    extend8: '',
    // 扩展字段9
    extend9: '',
    // 扩展字段10
    extend10: '',
    // 发票号，字符型(60)，文本控件，必填，同出货单号，允许修改
    invoiceNo: '',
    // 出货单号，字符型(60)，文本控件，必填，出货信息操作<确认>时带出，不允许修改
    exportNo: '',
    // 合同号，字符型(60)，文本控件，必填，出货信息操作<确认>时带出，不允许修改
    contractNo: '',
    // 发票客户，字符型(200)，文本控件，必填，出货信息操作<确认>时带出-客户，不允许修改
    invoiceCustomer: '',
    // 销售客户，字符型(200)，文本控件，必填，出货信息操作<确认>时带出-供应商，不允许修改
    salesCustomer: '',
    // 信用证号，字符型(60)，文本控件，非必填，用户录入
    lcNo: '',
    // 币种，字符型(10)，下拉框控件，必填，出货信息操作<确认>时带出，不允许修改
    currency: '',
    // 合计金额，数值型(19,4)，文本控件，必填，操作<确认>时系统带出，不允许修改，汇总出货信息表体金额
    totalAmount: '',
    // 唛头，字符型(300)，文本控件，非必填，出货信息操作<确认>时带出，不允许修改
    mark: '',
    // 代理费率%，数值型(19,6)，文本控件，必填，出货信息操作<确认>时带出，根据合同号关联【合同与协议】代理协议表头-代理费率，不允许修改
    agentRate: '',
    // 代理费（外币），数值型(19,6)，文本控件，必填，系统计算=合计金额*代理费率，不允许修改
    agentFeeForeign: '',
    // 预收款日期，日期型(10)，日期控件，非必填，用户录入
    prepayDate: '',
    // 作销日期，日期型(10)，日期控件，非必填，用户录入
    cancelDate: '',
    // 发票日期，日期型(10)，日期控件，非必填，用户录入
    invoiceDate: '',
    // 汇率，数值型(19,6)，文本控件，必填，根据当前月份+币种关联【企业自定义参数-企业汇率】取"汇率"栏位值，允许修改；<新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
    exchangeRate: '',
    // 代理费（不含税金额），数值型(19,2)，文本控件，必填，系统计算=代理费（价税合计）-代理费（不含税金额），当代理费（价税合计）变化时，要重新计算该栏位，不允许修改
    agentFeeExTax: '',
    // 代理费税额，数值型(19,2)，文本控件，必填，系统计算=代理费（价税合计）*0.06，当代理费（价税合计）变化时，要重新计算该栏位，不允许修改
    agentTax: '',
    // 代理费（价税合计），数值型(19,6)，文本控件，必填，系统计算=代理费（外币）*汇率，不允许修改
    agentFeeTotal: '',
    // 发送财务系统，字符型(10)，下拉框控件，必填，0是1否，默认为是，允许修改
    sendFinance: '',
    // 备注，字符型(200)，文本控件，非必填，用户录入
    remark: '',
    // 是否红冲，字符型(10)，下拉框控件，必填，0是1否，新增数据默认为1否；操作<红冲>成功时，改为0是，不允许修改
    isRedFlush: '',
    // 单据状态，字符型(10)，下拉框控件，非必填，0编制：未点击外销发票列表的"确认"功能按钮；1确认：点击外销发票列表的"确认"功能按钮，且成功提交；2作废：点击外销发票列表的"作废"功能按钮，且成功提交；不允许修改，置灰
    billStatus: '',
    // 确认时间，日期型(18)，日期控件，非必填，点击外销发票列表的"确认"功能按钮，且成功提交的时间：yyyy-mm-dd hh:mm:ss，不允许修改，置灰
    confirmTime: ''
  })

  // 校验规则
  const rules = {
    id: [
      {max: 40, message: '主键id长度不能超过 40位字节', trigger: 'blur'}
    ],
    businessType: [
      {max: 120, message: '业务类型长度不能超过 120位字节', trigger: 'blur'}
    ],
    dataState: [
      {required: true, message: '单据状态不能为空', trigger: 'blur'},
      {max: 20, message: '数据状态长度不能超过 20位字节', trigger: 'blur'}
    ],
    versionNo: [
      {max: 20, message: '版本号长度不能超过 20位字节', trigger: 'blur'}
    ],
    tradeCode: [
      {max: 20, message: '企业10位编码长度不能超过 20位字节', trigger: 'blur'}
    ],
    sysOrgCode: [
      {max: 20, message: '组织机构代码长度不能超过 20位字节', trigger: 'blur'}
    ],
    parentId: [
      {max: 40, message: '父级id长度不能超过 40位字节', trigger: 'blur'}
    ],
    createBy: [
      {required: true, message: '创建人不能为空', trigger: 'blur'},
      {max: 100, message: '创建人长度不能超过 100位字节', trigger: 'blur'}
    ],
    updateBy: [
      {max: 100, message: '更新人长度不能超过 100位字节', trigger: 'blur'}
    ],
    updateTime: [],
    insertUserName: [
      {max: 100, message: '插入用户名长度不能超过 100位字节', trigger: 'blur'}
    ],
    updateUserName: [
      {max: 100, message: '更新用户名长度不能超过 100位字节', trigger: 'blur'}
    ],
    extend1: [
      {max: 400, message: '扩展字段1长度不能超过 400位字节', trigger: 'blur'}
    ],
    extend2: [
      {max: 400, message: '扩展字段2长度不能超过 400位字节', trigger: 'blur'}
    ],
    extend3: [
      {max: 400, message: '扩展字段3长度不能超过 400位字节', trigger: 'blur'}
    ],
    extend4: [
      {max: 400, message: '扩展字段4长度不能超过 400位字节', trigger: 'blur'}
    ],
    extend5: [
      {max: 400, message: '扩展字段5长度不能超过 400位字节', trigger: 'blur'}
    ],
    extend6: [
      {max: 400, message: '扩展字段6长度不能超过 400位字节', trigger: 'blur'}
    ],
    extend7: [
      {max: 400, message: '扩展字段7长度不能超过 400位字节', trigger: 'blur'}
    ],
    extend8: [
      {max: 400, message: '扩展字段8长度不能超过 400位字节', trigger: 'blur'}
    ],
    extend9: [
      {max: 400, message: '扩展字段9长度不能超过 400位字节', trigger: 'blur'}
    ],
    extend10: [
      {max: 400, message: '扩展字段10长度不能超过 400位字节', trigger: 'blur'}
    ],
    invoiceNo: [
      {required: true, message: '发票号不能为空', trigger: 'blur'},
      {max: 60, message: '发票号长度不能超过 60位字节', trigger: 'blur'}
    ],
    exportNo: [
      {required: true, message: '出货单号不能为空', trigger: 'blur'},
      {max: 60, message: '出货单号长度不能超过 60位字节', trigger: 'blur'}
    ],
    contractNo: [
      {required: true, message: '合同号不能为空', trigger: 'blur'},
      {max: 60, message: '合同号长度不能超过 60位字节', trigger: 'blur'}
    ],
    invoiceCustomer: [
      {required: true, message: '发票客户不能为空', trigger: 'blur'},
      {max: 200, message: '发票客户长度不能超过 200位字节', trigger: 'blur'}
    ],
    salesCustomer: [
      {required: true, message: '销售客户不能为空', trigger: 'blur'},
      {max: 200, message: '销售客户长度不能超过 200位字节', trigger: 'blur'}
    ],
    lcNo: [
      {max: 60, message: '信用证号长度不能超过 60位字节', trigger: 'blur'}
    ],
    currency: [
      {required: true, message: '币种不能为空', trigger: 'blur'},
      {max: 10, message: '币种长度不能超过 10位字节', trigger: 'blur'}
    ],
    totalAmount: [
      {required: true, message: '合计金额不能为空', trigger: 'blur'},
      {type: 'number', message: '合计金额不是有效的数字!'},
    ],
    mark: [
      {max: 300, message: '唛头长度不能超过 300位字节', trigger: 'blur'}
    ],
    agentRate: [
      {required: true, message: '代理费率不能为空', trigger: 'blur'},
      {type: 'number', message: '代理费率不是有效的数字!'},
    ],
    agentFeeForeign: [
      {required: true, message: '代理费（外币）不能为空', trigger: 'blur'},
      {type: 'number', message: '代理费（外币）不是有效的数字!'},
    ],
    prepayDate: [],
    cancelDate: [],
    invoiceDate: [],
    exchangeRate: [
      {required: true, message: '汇率不能为空', trigger: 'blur'},
      {type: 'number', message: '汇率不是有效的数字!'},
    ],
    agentFeeExTax: [
      {required: true, message: '代理费（不含税金额）不能为空', trigger: 'blur'},
      {type: 'number', message: '代理费（不含税金额）不是有效的数字!'},
    ],
    agentTax: [
      {required: true, message: '代理费税额不能为空', trigger: 'blur'},
      {type: 'number', message: '代理费税额不是有效的数字!'},
    ],
    agentFeeTotal: [
      {required: true, message: '代理费（价税合计）不能为空', trigger: 'blur'},
      {type: 'number', message: '代理费（价税合计）不是有效的数字!'},
    ],
    sendFinance: [
      {required: true, message: '发送财务系统不能为空', trigger: 'blur'},
      {max: 10, message: '发送财务系统长度不能超过 10位字节', trigger: 'blur'}
    ],
    remark: [
      {max: 200, message: '备注长度不能超过 200位字节', trigger: 'blur'}
    ],
    isRedFlush: [
      {required: true, message: '是否红冲不能为空', trigger: 'blur'},
      {max: 10, message: '是否红冲长度不能超过 10位字节', trigger: 'blur'}
    ],
    billStatus: [
      {max: 10, message: '单据状态长度不能超过 10位字节', trigger: 'blur'}
    ],
    confirmTime: [

    ],
    createTime: [
      {required: true, message: '制单时间不能为空', trigger: 'blur'}
    ]
  }

  // PCode
  const pCode = ref('')


  // vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
  const formRef = ref(null);
  // 保存
  const saveLoading = ref(false)
  const handlerSave = () => {
    formRef.value
      .validate()
      .then(() => {
        saveLoading.value = true
        // 更新外销发票表头
        window.majesty.httpUtil.putAction(`${ycCsApi.bizExportGoodsSellHead.update}/${formData.id}`, formData).then(res => {
          if (res.code === 200) {
            message.success('修改成功!')
            Object.assign(formData, res.data)
          }else {
            message.error(res.message)
          }
        }).finally(()=>{
          saveLoading.value = false
        })

      })
      .catch(error => {
        console.log('validate failed', error);
      })
  };



  // 获取表单数据
  const getFormData = () => {
    window.majesty.httpUtil.getAction(`${ycCsApi.bizExportGoodsSellHead.getFormDataById}/${props.editConfig.editData.id}`, {}).then(res => {
      if (res.code === 200) {
        Object.assign(formData, res.data)
      }else {
        message.error(res.message)
      }
    })
  }
  const customerList = ref([]);
  const currList = ref([]);
  const formDataLoading = ref(false);
  const getCommonKeyValueList = async () => {
    formDataLoading.value = true;
    try {
      const res = await window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsHead.getCommonKeyValueList,{});
      if (res.code === 200) {
        customerList.value = res.data.customerList;
        currList.value = res.data.currList;
      }else {
        message.error(res.message);
      }
    }catch(err) {
      console.log(err);
    }finally {
      formDataLoading.value = false;
    }
  }
  // 初始化操作
  onMounted(() => {
    getPCode().then(res => {
      console.log('res', res)
      pCode.value = res;
    })
    getCommonKeyValueList()
    // 获取初始化数据
    getFormData()
    // 初始化数据
    // if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    //   Object.assign(formData, props.editConfig.editData);
    //   showDisable.value = false
    // }
    if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
      // Object.assign(formData, props.editConfig.editData);
      showDisable.value = true
    }
  });




  /* 确认 */
  const confirmOrderLoading = ref(false);
  const handleConfirm = ()=>{
    if (formData.dataState === '2'){
      message.warning('该数据已作废，不允许进行确认操作！')
      return
    }

    if (formData.dataState === '1'){
      message.warning('该数据已经确认，无需重复操作！')
      return
    }

    // 弹出确认框
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: '是否确认所选项？',
      onOk() {
        confirmOrderLoading.value = true
        window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsSellHead.confirm}`, formData)
          .then(res => {
            if (res.code === 200) {
              message.success("确认成功！")
              Object.assign(formData, res.data)
              emitEvent('export_confirm_invoice',true)
              // 修改状态为全部确认

              // onBack({
              //   editData: res.data,
              //   editStatus: editStatus.EDIT,
              //   isAllConfirmed: true
              // })
            } else {
              message.error(res.message)
            }
          }).finally(() => {
            confirmOrderLoading.value = false
          });
      },
      onCancel() {

      },
    });
  }


  /* 退单 */
  const backOrderLoading = ref(false);
  const handleBackOrder = () => {
    if (formData.dataState === '2'){
      message.warning('该数据已作废，不允许进行退单操作！')
      return
    }
    if (formData.dataState === '0'){
      message.warning('编制状态不允许进行退单操作！')
      return
    }
    // 弹出确认框
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: '是否退单所选项？',
      onOk() {
        backOrderLoading.value = true
        window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsSellHead.backOrder}`, formData)
          .then(res => {
            if (res.code === 200) {
              message.success("退单成功！")
              Object.assign(formData, res.data)
            } else {
              message.error(res.message)
            }
          }).finally(() => {
            backOrderLoading.value = false
          });
      },
      onCancel() {

      },
    });
  }



  /* 红冲 */
  const redFlushLoading = ref(false);
  const handleRedFlush = ()=>{
    if (formData.dataState === '2'){
      message.warning('该数据已作废，不允许进行红冲操作！')
      return
    }
    if (formData.isRedFlush === '1'){
      message.warning('该数据已经红冲，无需重复操作！')
      return
    }

    if (formData.dataState === '1'){
      message.warning('该数据已经确认，无需重复操作！')
      return
    }

    // 弹出确认框
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: '是否红冲所选项？',
      onOk() {
        redFlushLoading.value = true
        window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsSellHead.redFlush}`, formData)
          .then(res => {
            if (res.code === 200) {
              message.success("红冲成功！")
              Object.assign(formData, res.data)
            } else {
              message.error(res.message)
            }
          }).finally(() => {
            redFlushLoading.value = false
          });
      },
      onCancel() {

      },
    });
  }







  /* 监控formData中，dataState的变化 */
  watch(()=>formData.dataState, (newVal,oldVal)=>{
    if (props.editConfig.editStatus !== editStatus.SHOW) {
      if (newVal === '1' || newVal === '2') {
        showDisable.value = true
      }else {
        showDisable.value = false
      }
    }
  })


</script>

<style lang="less" scoped>


</style>



