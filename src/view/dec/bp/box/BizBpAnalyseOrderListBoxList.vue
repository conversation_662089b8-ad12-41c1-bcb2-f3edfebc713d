 <template>
  <!-- （第7条线）出料加工进口薄片-装箱列表 -->
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
          <div class="cs-action-btn-item" v-has="['yc-cs:iEBusiness:analyse:list:add']" v-if="props.showDisable === false">
            <a-button size="small" @click="handlerAdd"  :disabled="props.isAllConfirmed">
              <template #icon>
                <GlobalIcon type="plus" style="color:green"/>
              </template>
              {{localeContent('m.common.button.add')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:iEBusiness:analyse:list:delete']" v-if="props.showDisable === false">
            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete" :disabled="props.isAllConfirmed">
              <template #icon>
                <GlobalIcon type="delete" style="color:red"/>
              </template>
              {{localeContent('m.common.button.delete')}}
            </a-button>
          </div>
      </div>

      <!-- 表格区域 -->
      <div>
        <s-table
          ref="tableRef"
          class="cs-action-table-item remove-table-border-add-bg"
          size="small"
          :height="430"
          :scroll="{ y:'100%', x: 400 }"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
        >
          <!-- 空数据 -->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>




    <!-- 新增装箱数据弹框 -->
    <!-- 新增合同号 -->
    <cs-modal :visible="showBoxDialog"   title="分析单装箱信息"   :width="1000" :footer="false" @cancel="handleBack"  >
      <template #customContent>
        <biz-bp-add-box-info  ref="contractModalRef"  :head-id = "props.headId"  @editBack="handleBack"  @saveSuccess="handleSaveSuccess"></biz-bp-add-box-info>
      </template>
    </cs-modal>

  </section>


</template>

<script setup>
  /* 使用自定义 Hook 函数 */
  import {useCommon} from '@/view/common/useCommon'
  import {computed, createVNode, h, onMounted, provide, reactive, ref} from "vue";
  import {message, Modal} from "ant-design-vue";
  import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
  import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
  import {localeContent} from "@/view/utils/commonUtil";
  import ycCsApi from "@/api/ycCsApi";
  import {useRoute} from "vue-router";
  import {editStatus} from "@/view/common/constant";
  import {usePCode} from "@/view/common/usePCode";
  import CsModal from "@/components/modal/cs-modal.vue";
  import AddIncomingDialog from "@/view/dec/incoming/componment/AddIncomingDialog.vue";
  import BizBpAddBoxInfo from "@/view/dec/bp/componment/BizBpAddBoxInfo.vue";
  import {useColumnsRender} from "@/view/common/useColumnsRender";
  const { getPCode } = usePCode()
  const { cmbShowRender } = useColumnsRender()
  /* 引入通用方法 */
  const {
    editConfig,
    show,
    page,
    showSearch,
    headSearch,
    handleEditByRow,
    handleViewByRow,
    operationEdit,
    onPageChange,
    handleShowSearch,
    handlerSearch,
    dataSourceList,
    tableLoading,
    getTableScroll,
    exportLoading,
    ajaxUrl,
    doExport,
    handlerRefresh

  } = useCommon()



  defineOptions({
    name: 'BizBpAnalyseOrderListBoxList',
  });


  /* 组件参数 */
  const props = defineProps({
    headId:{
      type:String,
      default:()=>''
    },
    isAllConfirmed:{
      type:Boolean,
      default:()=>false
    },
    showDisable:{
      type:Boolean,
      default:()=>false
    }
  })


  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      title: '序号',
      width: 200,
      align: 'center',
      dataIndex: 'serialNo',
      key: 'serialNo',
    },
    {
      title: '集装箱规格',
      width: 200,
      align: 'center',
      dataIndex: 'containerSpec',
      key: 'containerSpec',
      customRender: ({ text }) => {
        return h('div', cmbShowRender(text,[],'CONTAINER_MODEL'))
      }
    },
    {
      title: '集装箱数',
      width: 200,
      align: 'center',
      dataIndex: 'containerCount',
      key: 'containerCount',
    },
    {
      title: '商品详情',
      width: 200,
      align: 'center',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '集装箱号',
      width: 200,
      align: 'center',
      dataIndex: 'containerNo',
      key: 'containerNo',
    },
    {
      title: '箱数',
      width: 200,
      align: 'center',
      dataIndex: 'boxCount',
      key: 'boxCount',
    },
    {
      title: '剩余箱数',
      width: 200,
      align: 'center',
      dataIndex: 'remainingBoxCount',
      key: 'remainingBoxCount'
    },
    {
      title: '备注',
      width: 200,
      align: 'center',
      dataIndex: 'note',
      key: 'note'
    }

  ])
  const importShow = ref(false)


  /* 查询数据 */
  const getList = async () => {
    tableLoading.value = true
    let params =  {
      parentId : props.headId
    }
    try {
      const res = await window.majesty.httpUtil.postAction(`${ycCsApi.bizBpAnalyseOrderListBox.list}?page=${page.current}&limit=${page.pageSize}`, params);
      dataSourceList.value = res.data
      page.total = res.total
      // 重置选择数据
      // restSelectData()
    }catch (error) {
      console.log(error)
    }finally {
      tableLoading.value = false
    }
  }



  const tableHeight = ref('')

  /* 引入表单数据 */
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData:[],
    loading: false,
  });



  /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
  const onSelectChange = (selectedRowKeys, rowSelectData) => {
    gridData.selectedData = rowSelectData;
    gridData.selectedRowKeys = selectedRowKeys;
  };


  /* 按钮loading */
  const deleteLoading = ref(false)


  // 显示装箱明细弹框
  const showBoxDialog = ref(false)
  const addLoading = ref(false)
  /* 新增数据 */
  const handlerAdd = ()=>{
    showBoxDialog.value = true
  }




  /* 删除数据 */
  const handlerDelete = () => {
    if (gridData.selectedRowKeys.length <= 0){
      message.warning('请选择一条数据')
      return
    }
    // 弹出确认框
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '删除',
      cancelText: '取消',
      content: '确认删除所选项吗？',
      onOk() {
        deleteLoading.value = true
        window.majesty.httpUtil.deleteAction(`${ycCsApi.bizBpAnalyseOrderListBox.delete}/${gridData.selectedRowKeys}/${props.headId}`).then(res => {
          if (res.code === 200) {
            message.success("删除成功")
            getList()
          }else {
            message.error(res.message)
          }
        }).finally(() => {
          deleteLoading.value = false
        })
      },
      onCancel() {

      },
    });

  }

  // 保存数据
  const handleSaveSuccess =  () =>{
    // 关闭弹框  刷新数据
    showBoxDialog.value = false
    getList()
  }


  // 返回
  const handleBack = ()=>{
   showBoxDialog.value = false
  }


  const pCode = ref(null)
  onMounted(fn => {

    // 获取PCode参数
    getPCode().then(res=>{
      // console.log('res',res)
      pCode.value = res;
    })
    getList()

  })




</script>

<style lang="less" scoped>


</style>
