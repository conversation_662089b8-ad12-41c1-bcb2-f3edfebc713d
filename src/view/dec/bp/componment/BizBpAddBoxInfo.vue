<template>
  <section style="margin: 0 10px;" class="cs-card-form">
    <!-- 上方表单区域 -->
    <div class="cs-form">
      <a-form
        ref="formRef"
        :model="formData"
        layout="inline"
        labelAlign="right"
        :label-col="{ style: { width: '100px' } }"
        class="grid-container two-columns"
        :rules="rules"
      >
        <a-form-item name="containerSpec" label="集装箱规格" class="grid-item" :colon="false" style="height: 24px;align-items: normal" >
          <cs-select
            :pCode="true"
            :options="pCode.CONTAINER_MODEL"
            :combine-display="true"
            allow-clear
            show-search
            v-model:value="formData.containerSpec"
            id="containerSpec"
          />
        </a-form-item>
        <a-form-item name="containerCount" label="集装箱数" class="grid-item" :colon="false" style="height: 24px;align-items: normal">
          <a-input-number
            style="width: 100%;"
            v-model:value="formData.containerCount"
            placeholder="请输入集装箱数"
            size="small"
            :min="1"
            @change="handleContainerCountChange"
          />
        </a-form-item>

        <a-form-item name="containerNo" label="集装箱号" class="grid-item merge-3" :colon="false" style="min-height: 24px; align-items: normal">
          <a-textarea
            v-model:value="formData.containerNo"
            placeholder="请输入集装箱号，多个用逗号分隔"
            size="small"
            :auto-size="{ minRows: 2, maxRows: 4 }"
            @blur="handleContainerNumbersChange"
          />
        </a-form-item>
      </a-form>
    </div>

    <!-- 下方表格区域 -->
    <div class="table-section">

      <!-- 操作按钮 -->
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:iEBusiness:analyse:dialog:add']">
          <a-button size="small" @click="handleAdd" :disabled="!canAddProduct">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            新增
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:iEBusiness:analyse:dialog:delete']">
          <a-button size="small" @click="handleDelete" :disabled="selectedRowKeys.length === 0">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            {{ localeContent('m.common.button.delete') }}
          </a-button>
        </div>
      </div>
      <s-table
        ref="tableRef"
        class="cs-action-item  remove-table-border-add-bg"
        size="small"
        :scroll="{ y: '100%',x:400 }"
        bordered
        :pagination="false"
        :columns="tableColumns"
        :data-source="tableData"
        :row-selection="{ selectedRowKeys:selectedRowKeys, onChange: onSelectChange }"
        row-key="id"
        column-drag
        :row-height="30"
        :row-hover-delay="5"
        :header-height="30"
        :range-selection="false"
      >
        <!-- 行内编辑模板 -->
        <template #bodyCell="{text, record, index, column, key }">

          <!-- 商品名称 -->
          <template v-if=" column.dataIndex === 'productName'">
            <cs-select v-model:value="record.productName"
                       @change="(val)=>{
                           handleProductChange(record,val)
                         }"
                       optionFilterProp="label" option-label-prop="key" allow-clear show-search id="productName"
                       clearable>
              <a-select-option v-for="item in productNameList" :key="item.value" :value="item.value"
                               :label="item.value">
                {{ item.value }}
              </a-select-option>
            </cs-select>
          </template>

          <!-- 箱数 -->
          <template v-if=" column.dataIndex === 'boxCount'">
            <a-input-number
              size="small"
              v-model:value="record.boxCount"
              style="width: 100%;height: 24px"
              :min="0"
              @blur="() => {
                  handleBoxCountChange(record)
                }"

              @keydown.enter="() => {
                  handleBoxCountChange(record)
                }"
              placeholder="请输入箱数"
            />
          </template>
          <!-- 自定义渲染 -->

          <template v-if="column.dataIndex === 'remainingBoxCount'">
              <span :class="{ 'text-red': record.remainingBoxCount < 0 }">
                {{ record.remainingBoxCount }}
              </span>
          </template>

        </template>

        <template #emptyText>
          <a-empty description="暂无数据"/>
        </template>
      </s-table>

    </div>



    <!-- 保存按钮区域 -->
    <div class="cs-submit-btn merge-3">
      <a-button size="small" type="primary" class="cs-margin-right" @click="handleSaveData" :loading="saveLoading">保存</a-button>

      <a-button size="small" class="cs-margin-right cs-warning" @click="handleBack">返回</a-button>


    </div>

  </section>

</template>

<script setup>
  import {ref, reactive, computed, nextTick, onMounted, watch} from 'vue'
  import {message} from 'ant-design-vue'
  import CsSelect from "@/components/select/CsSelect.vue";

  import {usePCode} from "@/view/common/usePCode";
  import {isNullOrEmpty} from "@/view/utils/common";
  import {localeContent} from "@/view/utils/commonUtil";
  import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
  import ycCsApi from "@/api/ycCsApi";

  const {getPCode} = usePCode()
  // 组件选项
  defineOptions({
    name: 'BizBpAddBoxInfo'
  })

  const props = defineProps({
    headId: {
      type: String,
      default: ''
    }
  })
  // 合同中该商品总箱数
  const contractAllBoxNumber = ref(null)

  // 定义事件
  const emit = defineEmits(['saveSuccess', 'editBack'])

  // 表单数据
  const formData = reactive({
    containerSpec: '', // 默认40GP
    containerCount: null, // 默认10个
    containerNo: null, // 默认集装箱号
    list:[]
  })


  const rules = {
    containerCount: [
      {required: true, message: '集装箱数不能为空', trigger: 'blur'},
    ],
    containerSpec: [
      {required: true, message: '集装箱规格不能为空', trigger: 'blur'},
    ],
    containerNo:[
      {required: true, message: '集装箱号不能为空', trigger: 'blur'},
    ]
  }

  // 表格数据（临时存储在前端）
  const tableData = ref([])

  // 商品名称下拉数据源
  const productNameList = ref([])

  // 选中的行
  const selectedRowKeys = ref([])
  const selectedRows = ref([])

  // 表单引用
  const formRef = ref()
  const tableRef = ref()

  // 表格列定义
  const tableColumns = [
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 150,
      align: 'center',
      ellipsis: true,
      editable: 'cellEditorSlot' // 支持行内编辑
    },
    {
      title: '箱数',
      dataIndex: 'boxCount',
      key: 'boxCount',
      width: 120,
      align: 'center',
      editable: 'cellEditorSlot' // 支持行内编辑
    },
    {
      title: '剩余箱数',
      dataIndex: 'remainingBoxCount',
      key: 'remainingBoxCount',
      width: 120,
      align: 'center'
    }
  ]

  // 计算属性：是否可以新增商品
  const canAddProduct = computed(() => {
    return formData.containerSpec && formData.containerCount > 0 && formData.containerNo
  })

  // 获取最大箱数限制
  const getMaxBoxCount = () => {
    return  999999999999999999999
  }

  // 处理集装箱规格变化
  const handleContainerTypeChange = (value) => {
    console.log('集装箱规格变化:', value)
  }

  // 处理集装箱数变化
  const handleContainerCountChange = (value) => {
    console.log('集装箱数变化:', value)
    updateRemainingBoxes()
  }

  watch(() => formData.containerCount, (newVal, oldVal) => {
    if (newVal !== oldVal) {
      updateRemainingBoxes();
    }
  });

  // 处理集装箱号变化
  const handleContainerNumbersChange = () => {
    console.log('集装箱号变化:', formData.containerNo)
  }

  // 处理箱数变化
  // 处理箱数变化
  const handleBoxCountChange = (record) => {
    if (isNullOrEmpty(record.boxCount)){
      record.remainingBoxCount = null
      message.error('箱数不能为空');
      // 触发编辑器重新渲染
      nextTick(() => {
        triggerEditor();
      });
      return;
    }
    // 判断商品名称是否输入 ，如果没输入商品名称，就报错
    if (isNullOrEmpty(record.productName)) {
      message.error('请先选择商品名称');
      return;
    }

    // 可选：触发所有相同商品的重新计算
    updateSameProductNameRemainingBoxes(record.productName)

    // 触发编辑器重新渲染
    nextTick(() => {
      triggerEditor();
    });

  };


  // 保存数据
  const save = () => {

  }

  // 更新剩余箱数
  const updateRemainingBoxes = () => {
    // 按 productName 分组计算
    const groups = {};
    tableData.value.forEach(record => {
      if (!groups[record.productName]) {
        groups[record.productName] = [];
      }
      groups[record.productName].push(record);
    });

    Object.values(groups).forEach(group => {
      let totalTempUsed = 0;
      group.forEach(record => {
        record.usedBoxes = record.boxCount * formData.containerCount;
        // console.log('已经使用箱数：'+record.usedBoxes)
        totalTempUsed += record.usedBoxes;
      });

      group.forEach(record => {
        const dbUsed = contractAllBoxNumber.value || 0;
        const remaining =   dbUsed - ( totalTempUsed );
        record.remainingBoxCount = remaining;
      });
    });
  }

  // 触发编辑器
  const triggerEditor = () => {
    const editConfigs = []
    tableData.value.forEach(row => {
      editConfigs.push({columnKey: 'boxCount', rowKey: row.id})
    })

    nextTick(() => {
      if (editConfigs.length > 0 && tableRef.value && typeof tableRef.value.openEditor === 'function') {
        tableRef.value.openEditor(editConfigs)
      }
    })
  }

  // 表格行选择
  const onSelectChange = (keys, data) => {
    selectedRowKeys.value = keys
    selectedRows.value = data
  }

  // 新增商品
  const handleAdd = async () => {
    try {
      await formRef.value.validate();
      if (!canAddProduct.value) {
        message.warning('请先完善集装箱信息');
        return;
      }

      // 检查是否有剩余箱数为 null 的数据
      if (tableData.value.some(item => item.remainingBoxCount === null)) {
        message.warning('存在数据未编辑，不能新增');
        return;
      }

      // 校验集装箱数量
      if (!validateContainerInfo(formData)) {
        return;
      }

      // 这里应该打开商品选择对话框
      // 模拟添加一个商品
      const newProduct = {
        // ID
        id: Date.now(),
        // 商品代码
        productCode: '',
        // 商品名称
        productName: '',
        // 箱数
        boxCount: null,
        // 剩余箱数
        remainingBoxCount: null,
        // 合同中该商品总箱数
        contractTotalBoxes: contractAllBoxNumber.value,
        usedBoxes: 0
      };

      tableData.value = [...tableData.value, newProduct];

      // 触发编辑器
      await nextTick();
      triggerEditor();

      // message.success('商品添加成功');
    } catch (error) {
      console.log('Validation Failed:', error);
    }
  };

  const handleDelete = () => {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请选择要删除的数据');
      return;
    }

    const deletedIds = new Set(selectedRowKeys.value);
    const deletedProductNames = new Set();

    // 收集被删除的商品名称
    selectedRowKeys.value.forEach(id => {
      const item = tableData.value.find(item => item.id === id);
      if (item) {
        deletedProductNames.add(item.productName);
      }
    });

    // 安全删除元素
    tableData.value = tableData.value.filter(item => !deletedIds.has(item.id));

    // 清空选择状态
    selectedRowKeys.value = [];
    selectedRows.value = [];

    // 删除后强制更新视图
    nextTick(() => {
      for (const productName of deletedProductNames) {
        updateSameProductNameRemainingBoxes(productName);
      }
    });

    message.success('删除成功');
  };

  // 根据商品名称修改箱数
  const updateSameProductNameRemainingBoxes = async (productName) => {
    let allConcatCount  = contractAllBoxNumber.value || 0;
    let allBoxCount = 0;
    let allCount = 0;



    // 根据商品名称找到所有对应数据
    let arrList = tableData.value.filter(record => record.productName === productName)
    // console.log('arrList', arrList)
    // 统计该商品名称下所有箱数
    arrList.forEach(item => {
      if (isNullOrEmpty(item.boxCount)){
        // 箱数为null 那么剩余箱数也变成null
        item.remainingBoxCount =  null
      }else {
        allBoxCount += (item.boxCount * formData.containerCount)
      }

    })
    // 统计集装箱 * 箱数 总数量
    //  allCount = formData.containerCount * allBoxCount

    tableData.value.forEach(record => {
      if (record.productName === productName) {
        record.remainingBoxCount = allConcatCount - allBoxCount
      }
    })

    // 4. 触发视图更新
    await nextTick();
    triggerEditor();
  };

  // 根据商品名称修改箱数
  const updateInnerCount = async (productName,count) => {

    tableData.value.forEach(record => {
      if (isNullOrEmpty(record.productName)) {
        record.remainingBoxCount =  null
      }else if (isNullOrEmpty(record.boxCount)) {
        record.remainingBoxCount = null
      }else {
        if (record.productName === productName) {
          record.remainingBoxCount += count
        }
      }

    })

    // 4. 触发视图更新
    await nextTick();
    triggerEditor();
  };





  /**
   * 商品名称发生变化 重新计算
   * @param record
   * @param value
   * @returns {Promise<void>}
   */
  const handleProductChange = async (record, value) => {
    let temp = {}
    Object.assign(temp,record);
    // 确保直接赋值触发响应式更新
    record.productName = value
    // 箱数 和 剩余箱数 重新赋值
    record.boxCount = null
    record.remainingBoxCount = null
    // 重新统计商品名称对应的总箱数
    let params = {
      parentId: props.headId,
      productName: value,
    }
    const res = await window.majesty.httpUtil.postAction(ycCsApi.bizBpAnalyseOrderListBox.getBoxCountByProductName,params);

    if (res.code === 200) {
      console.log('商品名称对应的总箱数', res.data)
      contractAllBoxNumber.value = res.data
    }else {
      message.warning('商品名称对应的总箱数获取失败');
    }

    // 重新计算剩余商品名称数量
    await updateInnerCount(temp.productName,temp.usedBoxes)


    // 触发视图更新（不加不会触发行编辑更新）
    await nextTick();
    triggerEditor();

  }

  const pCode = ref('')

  // 获取全部分析单表体的商品名称
  const getProductNameList = async () => {
    let params = {
      parentId: props.headId,
    }
    const res = await window.majesty.httpUtil.postAction(ycCsApi.bizBpAnalyseOrderListBox.getProductNameListByHeadId,params);

    if (res.code === 200) {
      console.log('商品名称列表', res.data)
      productNameList.value = res.data
    }else {
      message.warning('商品名称列表获取失败');
    }
  }



  // 校验集装箱数量
  const validateContainerInfo = (formData) => {
    // 检查集装箱号是否为空
    if (!formData.containerNo || formData.containerNo.trim() === '') {
      message.warning('请输入集装箱号');
      return false;
    }

    // 检查集装箱数量是否为正数
    if (!formData.containerCount || formData.containerCount <= 0) {
      message.warning('集装箱数量必须大于0');
      return false;
    }

    // 将集装箱号按照逗号分割，并去除空格
    const containerNumbers = formData.containerNo.split(',').map(item => item.trim()).filter(item => item !== '');

    // 检查分割后的集装箱号数量是否与集装箱数量一致
    if (formData.containerCount !== containerNumbers.length) {
      message.warning('集装箱数量与集装箱号数量不一致');
      return false;
    }

    // 检查是否有重复的集装箱号
    const uniqueContainers = new Set(containerNumbers);
    if (uniqueContainers.size !== containerNumbers.length) {
      message.warning('集装箱号不能重复，请检查输入');
      return false;
    }

    // 校验通过
    return true;
  }
  /**
   * 保存数据
   */
  const saveLoading = ref( false)
  const handleSaveData = async () => {
    // 校验集装箱数量
    if (!validateContainerInfo(formData)) {
      return;
    }

    // 校验表单数据是否符合规范
    try {
      saveLoading.value = true
      await formRef.value.validate();

      // 校验tableData是否为空
      if (isNullOrEmpty(tableData.value)) {
        message.warning('表体数据为空，不能保存');
        return;
      }
      // 校验表格数据是否符合规范
      // 1. 检查是否有剩余箱数为 null 的数据
      if (tableData.value.some(item => isNullOrEmpty(item.remainingBoxCount))) {
        message.warning('表体存在数据未编辑，不能保存');
        return;
      }
      if (tableData.value.some(item => isNullOrEmpty(item.productName))) {
        message.warning('表体存在数据未编辑，不能保存');
        return;
      }

      if (tableData.value.some(item => isNullOrEmpty(item.boxCount))) {
        message.warning('表体存在数据未编辑，不能保存');
        return;
      }
      // 2. 检查是否有箱数为 0 的数据
      if (tableData.value.some(item => item.boxCount === 0)) {
        message.warning('表体存在箱数为 0 的数据，不能保存');
        return;
      }
      // 3. 检查剩余箱数是否存在负数
      if (tableData.value.some(item => item.remainingBoxCount < 0)) {
        message.warning('表体存在剩余箱数为负数的数据，不能保存');
        return;
      }

        // 保存箱数
        const saveData = {
          containerSpec: formData.containerSpec,
          containerCount: formData.containerCount,
          containerNo: formData.containerNo,
          parentId: props.headId,
          list: tableData.value
        }


      const res = await window.majesty.httpUtil.postAction(ycCsApi.bizBpAnalyseOrderListBox.insertContainerList,saveData)
      if (res.code === 200) {
        message.success('保存成功');

        // 保存成功关闭弹框 刷新数据
        emit('saveSuccess');
      }else {
        message.warning(res.message);
      }

      // setTimeout(()=>{
      //   // 保存箱数
      //   const saveData = {
      //     containerSpec: formData.containerSpec,
      //     containerCount: formData.containerCount,
      //     containerNo: formData.containerNo,
      //     productList: tableData.value
      //   }
      //   console.log('保存数据未：', saveData);
      // },2000)


    }catch(err) {
      console.log('err', err)
    }finally {

      saveLoading.value = false
    }

  }

  // 返回
  const handleBack = () => {
    emit('editBack')
  }



  // 组件挂载后触发编辑器
  onMounted(() => {
    // 获取PCode参数
    getPCode().then(res => {
      console.log('PCode信息', res)
      pCode.value = res;
    })

    // 获取分析单商品名称列表
    getProductNameList()
    nextTick(() => {
      triggerEditor()
    })
  })
</script>

<style lang="less" scoped>
.text-red {
  color: red;
  font-size: 14px;
}
</style>
